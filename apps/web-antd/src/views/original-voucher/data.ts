// 发票和银行回单tab的表格字段和筛选schema定义
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { FormSchemaGetter } from '#/adapter/form';

// 发票列表表格字段
export const invoiceColumns: VxeGridProps['columns'] = [
  { title: '发票号码', field: 'digital_invoice_number' },
  { title: '凭证号', field: 'voucher_number' },
  { title: '发票状态', field: 'status' },
  { title: '发票类型', field: 'type' },
  { title: '开票日期', field: 'issue_date' },
  { title: '货物或劳务明细', field: 'goods_name' },
  { title: '销方名称', field: 'seller_name' },
  { title: '购方名称', field: 'buyer_name' },
  {
    title: '操作',
    field: 'action',
    slots: { default: 'action' },
    width: 120,
    fixed: 'right',
  },
];

// 发票列表筛选schema
export const invoiceQuerySchema: FormSchemaGetter = () => [
  {
    component: 'RangePicker',
    fieldName: 'dateRange',
    label: '开票日期',
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      placeholder: ['开始日期', '结束日期'],
    },
  },
  {
    component: 'Input',
    fieldName: 'voucher_number',
    label: '凭证编号',
    componentProps: { placeholder: '请输入凭证编号' },
  },
  {
    component: 'Select',
    fieldName: 'status',
    label: '发票状态',
    componentProps: {
      options: [
        { label: '全部', value: '' },
        { label: '已开票', value: '已开票' },
        { label: '已作废', value: '已作废' },
        { label: '已红冲', value: '已红冲' },
      ],
      allowClear: true,
      placeholder: '请选择状态',
    },
  },
  {
    component: 'Select',
    fieldName: 'input_output',
    label: '发票类型',
    componentProps: {
      options: [
        { label: '销项发票', value: 'output' },
        { label: '进项发票', value: 'input' },
      ],
      placeholder: '请选择发票类型',
    },
    required: true,
  },
  {
    component: 'Input',
    fieldName: 'company_name',
    label: '公司名称',
    required: true,
    componentProps: { placeholder: '请输入公司名称' },
  },
];

// 银行回单表格字段
export const bankColumns: VxeGridProps['columns'] = [
  { title: '公司名称', field: 'company_name' },
  { title: '交易时间', field: 'transaction_time' },
  { title: '月份', field: 'month' },
  { title: '类型', field: 'type' },
  { title: '凭证编号', field: 'voucher_number' },
  { title: '交易流水号', field: 'transaction_id' },
  { title: '账户名称', field: 'account_name' },
  { title: '银行名称', field: 'bank_name' },
  { title: '对方账户名称', field: 'conterpary_account_name' },
  { title: '金额', field: 'amount' },
  { title: '币种', field: 'currency' },
  { title: '摘要', field: 'summary' },
  { title: '备注', field: 'note' },
  {
    title: '操作',
    field: 'action',
    slots: { default: 'action' },
    width: 120,
    fixed: 'right',
  },
];

// 银行回单筛选schema
export const bankQuerySchema: FormSchemaGetter = () => [
  {
    component: 'RangePicker',
    fieldName: 'dateRange',
    label: '交易时间',
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      placeholder: ['开始日期', '结束日期'],
    },
  },
  {
    component: 'Input',
    fieldName: 'voucher_number',
    label: '凭证编号',
    componentProps: { placeholder: '请输入凭证编号' },
  },
  {
    component: 'Input',
    fieldName: 'month',
    label: '月份',
    componentProps: { placeholder: '如202502' },
  },
  {
    component: 'Select',
    fieldName: 'type',
    label: '类型',
    componentProps: {
      options: [
        { label: '全部', value: '' },
        { label: '收入', value: '收入' },
        { label: '支出', value: '支出' },
      ],
      allowClear: true,
      placeholder: '请选择类型',
    },
  },
  {
    component: 'Input',
    fieldName: 'company_name',
    label: '公司名称',
    required: true,
    componentProps: { placeholder: '请输入公司名称' },
  },
]; 
