# 原始发票界面

## 功能概述

原始发票界面是一个用于查看和管理发票及银行回单的页面，支持以下功能：

- 销项发票查询
- 进项发票查询  
- 银行回单查询
- 页面初始化时自动加载数据
- Tab切换时自动刷新数据
- 支持多种筛选条件

## 主要特性

### 1. 页面初始化
- 页面加载时会自动调用 `fetchData()` 方法获取数据
- 默认显示销项发票数据

### 2. Tab切换
- 支持三个Tab：销项发票、进项发票、银行回单
- 切换Tab时会自动重新获取对应类型的数据

### 3. 数据查询
- **发票查询**：支持按开票日期、凭证编号、发票状态、公司名称等条件筛选
- **银行回单查询**：支持按交易时间、凭证编号、月份、类型、公司名称等条件筛选

### 4. 必填参数验证
- 公司名称为必填参数，如果未填写会显示警告提示
- 发票查询时会自动设置 `input_output` 参数（进项/销项）

## API接口

### 发票列表接口
```
GET /api/invoice/list
```

**参数：**
- `company_name`: 公司名称（必填）
- `input_output`: 进项(input)或销项(output)（必填）
- `begin_time`: 开始时间，如2025-01-01（选填）
- `end_time`: 结束时间，如2025-01-22（选填）
- `status`: 状态（选填）
- `voucher_number`: 凭证编号（选填）

### 银行回单列表接口
```
GET /api/bank_receipts/list
```

**参数：**
- `company_name`: 公司名称（必填）
- `begin_time`: 开始时间，如2025-01-01（选填）
- `end_time`: 结束时间，如2025-01-22（选填）
- `month`: 月份，如202502（选填）
- `type`: 类型（选填）
- `voucher_number`: 凭证编号（选填）

## 文件结构

```
original-voucher/
├── index.vue          # 主组件文件
├── data.ts           # 表格列定义和表单schema
├── README.md         # 说明文档
└── api/
    ├── index.ts      # API接口定义
    └── types.ts      # TypeScript类型定义
```

## 使用方法

1. **页面访问**：直接访问原始发票页面，会自动加载销项发票数据

2. **切换数据类型**：点击页面顶部的Tab（销项发票/进项发票/银行回单）

3. **筛选数据**：
   - 填写筛选条件（公司名称为必填）
   - 点击"搜索"按钮

4. **查看原文件**：点击表格中的"查看原文件"链接

## 技术实现

- **框架**：Vue 3 + TypeScript
- **UI组件**：Ant Design Vue
- **表格组件**：VXE Table
- **表单组件**：Vben Form
- **HTTP请求**：Vben Request Client

## 注意事项

1. 公司名称是必填参数，请确保在查询前填写
2. 日期范围选择器支持快速选择和手动输入
3. 表格支持横向滚动，适配不同屏幕尺寸
4. 所有API请求都包含错误处理和加载状态
5. 页面支持响应式布局，在移动端也能正常使用
