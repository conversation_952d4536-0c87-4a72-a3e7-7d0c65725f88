# 原始发票界面

## 功能概述

原始发票界面是一个用于查看和管理发票及银行回单的页面，支持以下功能：

- 销项发票查询
- 进项发票查询  
- 银行回单查询
- 页面初始化时自动加载数据
- Tab切换时自动刷新数据
- 支持多种筛选条件

## 主要特性

### 1. 全局公司管理 🆕
- **全局状态同步**：与AI聊天窗口共享相同的公司选择状态
- **自动默认值**：公司名称字段自动填入当前全局选中的公司
- **下拉选择**：公司名称改为下拉选择框，支持搜索和过滤
- **状态监听**：监听全局公司变化，自动刷新数据

### 2. 页面初始化
- 页面加载时自动获取公司列表和数据
- 默认显示销项发票数据
- 智能设置默认公司选择

### 3. Tab切换
- 支持三个Tab：销项发票、进项发票、银行回单
- 切换Tab时会自动重新获取对应类型的数据
- 保持公司选择状态

### 4. 数据查询
- **发票查询**：支持按开票日期、凭证编号、发票状态、公司名称等条件筛选
- **银行回单查询**：支持按交易时间、凭证编号、月份、类型、公司名称等条件筛选

### 5. 智能参数处理
- 公司名称优先使用表单选择，其次使用全局状态
- 发票查询时会自动设置 `input_output` 参数（进项/销项）
- 友好的用户提示，无强制验证警告

## API接口

### 发票列表接口
```
GET /api/invoice/list
```

**参数：**
- `company_name`: 公司名称（必填）
- `input_output`: 进项(input)或销项(output)（必填）
- `begin_time`: 开始时间，如2025-01-01（选填）
- `end_time`: 结束时间，如2025-01-22（选填）
- `status`: 状态（选填）
- `voucher_number`: 凭证编号（选填）

### 银行回单列表接口
```
GET /api/bank_receipts/list
```

**参数：**
- `company_name`: 公司名称（必填）
- `begin_time`: 开始时间，如2025-01-01（选填）
- `end_time`: 结束时间，如2025-01-22（选填）
- `month`: 月份，如202502（选填）
- `type`: 类型（选填）
- `voucher_number`: 凭证编号（选填）

## 文件结构

```
original-voucher/
├── index.vue          # 主组件文件
├── data.ts           # 表格列定义和表单schema
├── README.md         # 说明文档
└── api/
    ├── index.ts      # API接口定义
    └── types.ts      # TypeScript类型定义
```

## 使用方法

1. **页面访问**：
   - 直接访问原始发票页面，会自动加载公司列表和销项发票数据
   - 公司名称会自动设置为当前全局选中的公司

2. **公司选择**：
   - 点击公司名称下拉框选择不同公司
   - 支持输入搜索公司名称
   - 选择后会自动刷新对应公司的数据

3. **切换数据类型**：点击页面顶部的Tab（销项发票/进项发票/银行回单）

4. **筛选数据**：
   - 填写其他筛选条件（日期范围、凭证编号等）
   - 点击"搜索"按钮

5. **查看原文件**：点击表格中的"查看原文件"链接

6. **全局状态同步**：
   - 在AI聊天窗口选择公司会自动同步到此页面
   - 在此页面选择公司也会同步到其他相关页面

## 技术实现

- **框架**：Vue 3 + TypeScript
- **UI组件**：Ant Design Vue
- **表格组件**：VXE Table
- **表单组件**：Vben Form
- **HTTP请求**：Vben Request Client
- **状态管理**：Pinia Store
- **全局状态**：useCompanySelectionStore

## 注意事项

1. **全局状态依赖**：页面依赖全局公司状态，确保AI聊天窗口已正确初始化
2. **公司列表获取**：页面初始化时会自动获取公司列表，网络异常时会显示默认公司
3. **状态同步**：公司选择变化会触发数据自动刷新，可能有短暂加载状态
4. **持久化存储**：公司选择状态会保存到localStorage，页面刷新后保持选择
5. **日期范围选择器**：支持快速选择和手动输入
6. **表格适配**：支持横向滚动，适配不同屏幕尺寸
7. **错误处理**：所有API请求都包含错误处理和加载状态
8. **响应式布局**：在移动端也能正常使用
