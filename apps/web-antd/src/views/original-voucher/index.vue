<script setup lang="ts">
  import { computed, onMounted, ref, watch } from 'vue';

  import { message } from 'ant-design-vue';

  import { useVbenForm } from '#/adapter/form';
  import { useVbenVxeGrid } from '#/adapter/vxe-table';
  import { getBankReceiptList, getInvoiceList } from '#/api/original-voucher';
  import type { BankReceiptQueryParams, InvoiceQueryParams } from '#/api/original-voucher';

  import {
    bankColumns,
    bankQuerySchema,
    invoiceColumns,
    invoiceQuerySchema,
  } from './data';

  const tabList = [
    { key: 'output', label: '销项发票' },
    { key: 'input', label: '进项发票' },
    { key: 'bank', label: '银行回单' },
  ];
  const activeTab = ref('output');

  // 动态schema和columns
  const formSchema = computed(() => {
    if (activeTab.value === 'bank') return bankQuerySchema();
    return invoiceQuerySchema();
  });
  const tableColumns = computed(() => {
    if (activeTab.value === 'bank') return bankColumns;
    return invoiceColumns;
  });

  // 数据源
  const tableData = ref([]);
  const loading = ref(false);

  // 用于强制刷新表单和表格
  const formKey = computed(() => `${activeTab.value}-form`);
  const tableKey = computed(() => `${activeTab.value}-table`);

  // 筛选表单
  const [BasicForm, formApi] = useVbenForm({
    schema: formSchema.value,
    wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  });

  // 表格
  const [BasicTable] = useVbenVxeGrid({
    // 美化表格
    bordered: true,
    columns: tableColumns.value,
    data: tableData,
    id: 'original-voucher-table',
    loading,
    pagerConfig: { enabled: true },
    rowConfig: { keyField: '_id' },
    scroll: { x: 1200 },
  } as any);

  // 查询方法
  async function fetchData() {
    loading.value = true;
    try {
      const values = await formApi.getValues();

      if (activeTab.value === 'bank') {
        // 银行回单查询
        const [begin_time, end_time] = values.dateRange || [];
        const params: BankReceiptQueryParams = {
          company_name: values.company_name || '',
          begin_time,
          end_time,
          month: values.month,
          type: values.type,
          voucher_number: values.voucher_number,
        };

        // 银行回单必须有公司名称
        if (!params.company_name) {
          message.warning('请输入公司名称');
          return;
        }

        // 过滤掉空值参数
        Object.keys(params).forEach(key => {
          const value = params[key as keyof BankReceiptQueryParams];
          if (value === undefined || value === null || value === '') {
            delete params[key as keyof BankReceiptQueryParams];
          }
        });

        console.log('银行回单查询参数:', params);
        const response = await getBankReceiptList(params);

        if (response.status === 'success') {
          tableData.value = response.data || [];
          console.log('获取银行回单数据成功:', response.data?.length || 0, '条记录');
        } else {
          message.error(response.message || '获取银行回单数据失败');
          tableData.value = [];
        }
      } else {
        // 发票查询
        const [begin_time, end_time] = values.dateRange || [];
        const params: InvoiceQueryParams = {
          company_name: values.company_name || '',
          input_output: activeTab.value as 'input' | 'output',
          begin_time,
          end_time,
          status: values.status,
          voucher_number: values.voucher_number,
        };

        // 发票查询必须有公司名称
        if (!params.company_name) {
          message.warning('请输入公司名称');
          return;
        }

        // 过滤掉空值参数
        Object.keys(params).forEach(key => {
          const value = params[key as keyof InvoiceQueryParams];
          if (value === undefined || value === null || value === '') {
            delete params[key as keyof InvoiceQueryParams];
          }
        });

        console.log('发票查询参数:', params);
        const response = await getInvoiceList(params);

        if (response.status === 'success') {
          tableData.value = response.data || [];
          console.log('获取发票数据成功:', response.data?.length || 0, '条记录');
        } else {
          message.error(response.message || '获取发票数据失败');
          tableData.value = [];
        }
      }
    } catch (error: any) {
      console.error('请求失败:', error);
      message.error(error?.message || '请求失败');
      tableData.value = [];
    } finally {
      loading.value = false;
    }
  }

  // 页面初始化时获取数据
  onMounted(() => {
    fetchData();
  });

  // tab切换时自动刷新数据
  watch(activeTab, () => {
    fetchData();
  });

  // 搜索按钮点击事件
  function handleSearch() {
    fetchData();
  }

  // 操作按钮
  function handleAdd() {
    message.info('新增加原始凭证功能开发中');
  }

  function handleAI() {
    message.info('AI记账功能开发中');
  }
</script>

<template>
  <a-card class="voucher-card" :body-style="{ padding: '24px 24px 12px 24px' }">
    <div class="voucher-header">
      <a-tabs v-model:active-key="activeTab" class="voucher-tabs">
        <a-tab-pane v-for="tab in tabList" :key="tab.key" :tab="tab.label" />
      </a-tabs>
    </div>
    <div class="voucher-filter-row">
      <div class="voucher-filter-form">
        <BasicForm :key="formKey" />
      </div>
      <div class="voucher-filter-actions">
        <a-button type="primary" @click="handleSearch">搜索</a-button>
        <a-button class="ml-2" @click="handleAdd">新增加原始凭证</a-button>
        <a-button class="ai-btn ml-2" @click="handleAI">AI记账</a-button>
      </div>
    </div>
    <div class="voucher-table-wrap">
      <BasicTable :key="tableKey">
        <template #action="{ row }">
          <a
            :href="row.url || '#'"
            target="_blank"
            title="查看原文件"
            class="table-action-link"
          >
            查看原文件
          </a>
        </template>
      </BasicTable>
    </div>
  </a-card>
</template>

<style scoped>
  .voucher-card {
    max-width: 1600px;
    margin: 0 auto;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px #f0f1f2;
  }

  .voucher-header {
    margin-bottom: 8px;
  }

  .voucher-tabs {
    font-size: 16px;
  }

  .voucher-filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: flex-end;
    justify-content: space-between;
    margin-bottom: 16px;
  }

  .voucher-filter-form {
    flex: 1 1 0%;
    min-width: 320px;
  }

  .voucher-filter-actions {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: flex-end;
    min-width: 320px;
  }

  .ai-btn {
    color: #fff;
    background: linear-gradient(90deg, #4f8cff 0%, #6ad1ff 100%);
    border: none;
  }

  .voucher-table-wrap {
    margin-top: 8px;
  }

  .table-action-link {
    color: #1677ff;
    text-decoration: underline;
    cursor: pointer;
  }

  :deep(.ant-table) {
    font-size: 14px;
  }

  :deep(.ant-table-thead > tr > th) {
    font-weight: 600;
    background: #f7f8fa;
  }

  :deep(.ant-pagination) {
    justify-content: flex-end;
    margin-top: 16px;
  }
</style>
