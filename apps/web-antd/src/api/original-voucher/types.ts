// 原始发票相关的类型定义文件

// 发票查询参数接口
export interface InvoiceQueryParams {
  company_name: string; // 公司名称，必填
  input_output: 'input' | 'output'; // 进项、销项，必填
  begin_time?: string; // 开始时间，如2025-01-01，选填
  end_time?: string; // 结束时间，如2025-01-22，选填
  status?: string; // 状态，选填
  voucher_number?: string; // 凭证编号，选填
}

// 银行回单查询参数接口
export interface BankReceiptQueryParams {
  company_name: string; // 公司名称，必填
  begin_time?: string; // 开始时间，如2025-01-01，选填
  end_time?: string; // 结束时间，如2025-01-22，选填
  month?: string; // 月份，如202502，选填
  type?: string; // 类型，选填
  voucher_number?: string; // 凭证编号，选填
}

// 发票数据接口（根据实际API响应更新）
export interface InvoiceData {
  _id: string;
  digital_invoice_number: string; // 发票号
  voucher_number: string; // 凭证号
  status: string; // 状态
  type: string; // 发票类型
  month: string; // 月份，如202502
  voucher_id: string; // 凭证ID
  issue_date: string; // 开票日期
  goods_name: string; // 货物或劳务明细
  seller_name: string; // 销方名称
  buyer_name: string; // 购方名称
}

// 银行回单数据接口
export interface BankReceiptData {
  _id: string;
  company_name: string; // 公司名称
  timestamp: string;
  month: string; // 月份
  type: string; // 类型
  voucher_id: string;
  transaction_id: string; // 交易流水号
  transaction_time: string; // 交易时间
  account_number: string;
  account_name: string; // 账户名称
  bank_name: string; // 银行名称
  conterpary_account_number: string | null;
  conterpary_account_name: string; // 对方账户名称
  conterpary_bank_name: string | null;
  amount: number; // 金额
  currency: string; // 币种
  summary: string; // 摘要
  note: string | null; // 备注
  url: string;
  desc: string;
  sequence_number: number;
  source_file: string;
  created_at: string;
  updated_at: string;
}

// 业务层API响应接口（内层数据）
export interface BusinessApiResponse<T> {
  status: string;
  message: string;
  data: T[];
}

// 完整的API响应接口（包含axios响应信息）
export interface FullApiResponse<T> {
  data: BusinessApiResponse<T>; // 业务数据
  status: number; // HTTP状态码
  statusText: string; // HTTP状态文本
  headers: Record<string, any>; // 响应头
  config: Record<string, any>; // 请求配置
  request?: any; // 请求对象
}

// 兼容旧版本的API响应接口
export interface ApiResponse<T> {
  status: string;
  message: string;
  data: T[];
}

// 表单值接口
export interface FormValues {
  dateRange?: [string, string];
  company_name?: string;
  voucher_number?: string;
  status?: string;
  month?: string;
  type?: string;
}
