import { RequestClient } from '@vben/request';
import type {
  ApiResponse,
  BankReceiptData,
  BankReceiptQueryParams,
  InvoiceData,
  InvoiceQueryParams,
} from './types';

// 创建专用的请求客户端
const requestClient = new RequestClient({
  baseURL: '/api',
  timeout: 10000,
});

// 导出类型定义
export type {
  ApiResponse,
  BankReceiptData,
  BankReceiptQueryParams,
  InvoiceData,
  InvoiceQueryParams,
} from './types';

/**
 * 查询发票列表
 * @param params 查询参数
 * @returns 发票列表
 */
export function getInvoiceList(params: InvoiceQueryParams) {
  return requestClient.get<ApiResponse<InvoiceData>>('/invoice/list', { params });
}

/**
 * 查询银行回单列表
 * @param params 查询参数
 * @returns 银行回单列表
 */
export function getBankReceiptList(params: BankReceiptQueryParams) {
  return requestClient.get<ApiResponse<BankReceiptData>>('/bank_receipts/list', { params });
}
